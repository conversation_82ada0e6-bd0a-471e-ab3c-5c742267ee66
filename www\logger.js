var exec = require('cordova/exec');

// LogLevel constants for use in Cordova apps
exports.LogLevel = {
    Debug: 'debug',
    Error: 'error',
    Info: 'important'
};

/**
 * Log a message with a specific level
 * @param {string} level - The log level ('debug', 'info', 'error')
 * @param {string} sourceClass - The class name (optional)
 * @param {string} sourceMethod - The method name (optional)
 * @param {string} message - The message to log
 * @param {function} success - Success callback
 * @param {function} error - Error callback
 */

const logFolder = "LogFiles"

exports.loggerWithLevel = function (level, sourceClass, sourceMethod, message, success, error) {
    exec(success, error, 'Logger', 'loggerWithLevel', [{ userId: logFolder, level: level, sourceClass: sourceClass, sourceMethod: sourceMethod, message: message }]);
};

/**
 * Set the log level for filtering messages
 * @param {string} level - The log level ('debug', 'info', 'error')
 * @param {function} success - Success callback
 * @param {function} error - Error callback
 */
exports.setLogLevel = function (level, success, error) {
    if (!level) {
        if (error) error('Log level is required');
        return;
    }
    exec(success, error, 'Logger', 'setLogLevel', [level]);
};

/**
 * Get the content of the log file for a specific user
 * @param {function} success - Success callback (receives log content as string)
 * @param {function} error - Error callback
 */
exports.getLogFileContent = function (success, error) {
    // If no callbacks provided, return a Promise
    if (!success && !error) {
        return new Promise((resolve, reject) => {
            exec(resolve, reject, 'Logger', 'getLogFileContent', [{ userId: logFolder }]);
        });
    }
    exec(success, error, 'Logger', 'getLogFileContent', [{ userId: logFolder }]);
};

/**
 * Clear the log file for a specific user
 * @param {function} success - Success callback
 * @param {function} error - Error callback
 */
exports.clearLogFile = function (success, error) {
    exec(success, error, 'Logger', 'clearLogFile', [{ userId: logFolder}]);
};

/**
 * Get the content of the backup log file for a specific user
 * @param {function} success - Success callback (receives backup log content as string)
 * @param {function} error - Error callback
 */
exports.getBackupLogFileContent = function (success, error) {
    exec(success, error, 'Logger', 'getBackupLogFileContent', [{ userId: logFolder }]);
};

/**
 * Copy the log file to backup for a specific user
 * @param {function} success - Success callback
 * @param {function} error - Error callback
 */
exports.copyLogToBackup = function (success, error) {
    exec(success, error, 'Logger', 'copyLogToBackup', [{ userId: logFolder }]);
};

/**
 * Convenience methods for different log levels
 */
exports.logDebug = function (sourceClass, sourceMethod, message, success, error) {
    if (!message) {
        if (error) error('Message is required');
        return;
    }
    exec(success, error, 'Logger', 'logDebug', [{ 
        userId: logFolder, 
        level: exports.LogLevel.Debug,
        message: message, 
        sourceClass: sourceClass, 
        sourceMethod: sourceMethod 
    }]);
};

exports.logInfo = function (sourceClass, sourceMethod, message, success, error) {
    if (!message) {
        if (error) error('Message is required');
        return;
    }
    exec(success, error, 'Logger', 'logInfo', [{ 
        userId: logFolder, 
        level: exports.LogLevel.Info,
        message: message, 
        sourceClass: sourceClass, 
        sourceMethod: sourceMethod 
    }]);
};

exports.logError = function (sourceClass, sourceMethod, message, success, error) {
    if (!message) {
        if (error) error('Message is required');
        return;
    }
    exec(success, error, 'Logger', 'logError', [{ 
        userId: logFolder, 
        level: exports.LogLevel.Error,
        message: message, 
        sourceClass: sourceClass, 
        sourceMethod: sourceMethod 
    }]);
};

exports.getLogFileURL = function (success, error) {
    exec(success, error, 'Logger', 'getLogFileURL', [{ userId: logFolder }]);
};

exports.getBackupLogFileURL = function (success, error) {
    exec(success, error, 'Logger', 'getBackupLogFileURL', [{ userId: logFolder }]);
};

exports.getLogLevel = function (success, error) {
    exec(success, error, 'Logger', 'getLogLevel', []);
};
